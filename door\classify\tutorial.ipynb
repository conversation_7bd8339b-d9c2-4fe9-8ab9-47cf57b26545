{"cells": [{"cell_type": "markdown", "metadata": {"id": "t6MPjfT5NrKQ"}, "source": ["<div align=\"center\">\n", "\n", "  <a href=\"https://ultralytics.com/yolov5\" target=\"_blank\">\n", "    <img width=\"1024\", src=\"https://raw.githubusercontent.com/ultralytics/assets/master/yolov5/v70/splash.png\"></a>\n", "\n", "\n", "<br>\n", "  <a href=\"https://bit.ly/yolov5-paperspace-notebook\"><img src=\"https://assets.paperspace.io/img/gradient-badge.svg\" alt=\"Run on Gradient\"></a>\n", "  <a href=\"https://colab.research.google.com/github/ultralytics/yolov5/blob/master/classify/tutorial.ipynb\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"></a>\n", "  <a href=\"https://www.kaggle.com/ultralytics/yolov5\"><img src=\"https://kaggle.com/static/images/open-in-kaggle.svg\" alt=\"Open In Kaggle\"></a>\n", "<br>\n", "\n", "This <a href=\"https://github.com/ultralytics/yolov5\">YOLOv5</a> 🚀 notebook by <a href=\"https://ultralytics.com\">Ultralytics</a> presents simple train, validate and predict examples to help start your AI adventure.<br>See <a href=\"https://github.com/ultralytics/yolov5/issues/new/choose\">GitHub</a> for community support or <a href=\"https://ultralytics.com/contact\">contact us</a> for professional support.\n", "\n", "</div>"]}, {"cell_type": "markdown", "metadata": {"id": "7mGmQbAO5pQb"}, "source": ["# Setup\n", "\n", "Clone GitHub [repository](https://github.com/ultralytics/yolov5), install [dependencies](https://github.com/ultralytics/yolov5/blob/master/requirements.txt) and check PyTorch and GPU."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wbvMlHd_QwMG", "outputId": "43b2e1b5-78d9-4e1d-8530-ee9779bba160"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["YOLOv5 🚀 v6.2-258-g7fc7ed7 Python-3.7.15 torch-1.12.1+cu113 CUDA:0 (Tesla T4, 15110MiB)\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Setup complete ✅ (2 CPUs, 12.7 GB RAM, 22.6/78.2 GB disk)\n"]}], "source": ["!git clone https://github.com/ultralytics/yolov5  # clone\n", "%cd yolov5\n", "%pip install -qr requirements.txt  # install\n", "\n", "import torch\n", "import utils\n", "display = utils.notebook_init()  # checks"]}, {"cell_type": "markdown", "metadata": {"id": "4JnkELT0cIJg"}, "source": ["# 1. Predict\n", "\n", "`classify/predict.py` runs YOLOv5 Classifcation inference on a variety of sources, downloading models automatically from the [latest YOLOv5 release](https://github.com/ultralytics/yolov5/releases), and saving results to `runs/predict-cls`. Example inference sources are:\n", "\n", "```shell\n", "python classify/predict.py --source 0  # webcam\n", "                              img.jpg  # image \n", "                              vid.mp4  # video\n", "                              screen  # screenshot\n", "                              path/  # directory\n", "                              'path/*.jpg'  # glob\n", "                              'https://youtu.be/Zgi9g1ksQHc'  # YouTube\n", "                              'rtsp://example.com/media.mp4'  # RTSP, RTMP, HTTP stream\n", "```"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zR9ZbuQCH7FX", "outputId": "1b610787-7cf7-4c33-aac2-aa50fbb84a94"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mclassify/predict: \u001b[0mweights=['yolov5s-cls.pt'], source=data/images, data=data/coco128.yaml, imgsz=[224, 224], device=, view_img=False, save_txt=True, nosave=False, augment=False, visualize=False, update=False, project=runs/predict-cls, name=exp, exist_ok=False, half=False, dnn=False, vid_stride=1\n", "YOLOv5 🚀 v6.2-258-g7fc7ed7 Python-3.7.15 torch-1.12.1+cu113 CUDA:0 (Tesla T4, 15110MiB)\n", "\n", "Downloading https://github.com/ultralytics/yolov5/releases/download/v6.2/yolov5s-cls.pt to yolov5s-cls.pt...\n", "100% 10.5M/10.5M [00:03<00:00, 2.94MB/s]\n", "\n", "Fusing layers... \n", "Model summary: 117 layers, 5447688 parameters, 0 gradients, 11.4 GFLOPs\n", "image 1/2 /content/yolov5/data/images/bus.jpg: 224x224 minibus 0.39, police van 0.24, amphibious vehicle 0.05, recreational vehicle 0.04, trolleybus 0.03, 3.9ms\n", "image 2/2 /content/yolov5/data/images/zidane.jpg: 224x224 suit 0.38, bow tie 0.19, bridegroom 0.18, rugby ball 0.04, stage 0.02, 4.1ms\n", "Speed: 0.3ms pre-process, 4.0ms inference, 1.5ms NMS per image at shape (1, 3, 224, 224)\n", "Results saved to \u001b[1mruns/predict-cls/exp\u001b[0m\n"]}], "source": ["!python classify/predict.py --weights yolov5s-cls.pt --img 224 --source data/images\n", "# display.Image(filename='runs/predict-cls/exp/zidane.jpg', width=600)"]}, {"cell_type": "markdown", "metadata": {"id": "hkAzDWJ7cWTr"}, "source": ["&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n", "<img align=\"left\" src=\"https://user-images.githubusercontent.com/26833433/202808393-50deb439-ae1b-4246-a685-7560c9b37211.jpg\" width=\"600\">"]}, {"cell_type": "markdown", "metadata": {"id": "0eq1SMWl6Sfn"}, "source": ["# 2. <PERSON><PERSON><PERSON>\n", "Validate a model's accuracy on the [Imagenet](https://image-net.org/) dataset's `val` or `test` splits. Models are downloaded automatically from the [latest YOLOv5 release](https://github.com/ultralytics/yolov5/releases). To show results by class use the `--verbose` flag."]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WQPtK1QYVaD_", "outputId": "92de5f34-cf41-49e7-b679-41db94e995ac"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["--2022-11-18 21:48:38--  https://image-net.org/data/ILSVRC/2012/ILSVRC2012_img_val.tar\n", "Resolving image-net.org (image-net.org)... ************\n", "Connecting to image-net.org (image-net.org)|************|:443... connected.\n", "HTTP request sent, awaiting response... 200 OK\n", "Length: 6744924160 (6.3G) [application/x-tar]\n", "Saving to: ‘ILSVRC2012_img_val.tar’\n", "\n", "ILSVRC2012_img_val. 100%[===================>]   6.28G  7.15MB/s    in 11m 13s \n", "\n", "2022-11-18 21:59:52 (9.55 MB/s) - ‘ILSVRC2012_img_val.tar’ saved [6744924160/6744924160]\n", "\n"]}], "source": ["# Download Imagenet val (6.3G, 50000 images)\n", "!bash data/scripts/get_imagenet.sh --val"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "X58w8JLpMnjH", "outputId": "9961ad87-d639-4489-b578-0a0578fefaab"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mclassify/val: \u001b[0mdata=../datasets/imagenet, weights=['yolov5s-cls.pt'], batch_size=128, imgsz=224, device=, workers=8, verbose=True, project=runs/val-cls, name=exp, exist_ok=False, half=True, dnn=False\n", "YOLOv5 🚀 v6.2-258-g7fc7ed7 Python-3.7.15 torch-1.12.1+cu113 CUDA:0 (Tesla T4, 15110MiB)\n", "\n", "Fusing layers... \n", "Model summary: 117 layers, 5447688 parameters, 0 gradients, 11.4 GFLOPs\n", "validating: 100% 391/391 [04:48<00:00,  1.35it/s]\n", "                   Class      Images    top1_acc    top5_acc\n", "                     all       50000       0.715       0.902\n", "                   tench          50        0.94        0.98\n", "                goldfish          50        0.88        0.92\n", "       great white shark          50        0.78        0.96\n", "             tiger shark          50        0.68        0.96\n", "        hammerhead shark          50        0.82        0.92\n", "            electric ray          50        0.76         0.9\n", "                stingray          50         0.7         0.9\n", "                    cock          50        0.78        0.92\n", "                     hen          50        0.84        0.96\n", "                 ostrich          50        0.98           1\n", "               brambling          50         0.9        0.96\n", "               goldfinch          50        0.92        0.98\n", "             house finch          50        0.88        0.96\n", "                   junco          50        0.94        0.98\n", "          indigo bunting          50        0.86        0.88\n", "          American robin          50         0.9        0.96\n", "                  bulbul          50        0.84        0.96\n", "                     jay          50         0.9        0.96\n", "                  magpie          50        0.84        0.96\n", "               chickadee          50         0.9           1\n", "         American dipper          50        0.82        0.92\n", "                    kite          50        0.76        0.94\n", "              bald eagle          50        0.92           1\n", "                 vulture          50        0.96           1\n", "          great grey owl          50        0.94        0.98\n", "         fire salamander          50        0.96        0.98\n", "             smooth newt          50        0.58        0.94\n", "                    newt          50        0.74         0.9\n", "      spotted salamander          50        0.86        0.94\n", "                 axolotl          50        0.86        0.96\n", "       American bullfrog          50        0.78        0.92\n", "               tree frog          50        0.84        0.96\n", "             tailed frog          50        0.48         0.8\n", "   loggerhead sea turtle          50        0.68        0.94\n", "  leatherback sea turtle          50         0.5         0.8\n", "              mud turtle          50        0.64        0.84\n", "                terrapin          50        0.52        0.98\n", "              box turtle          50        0.84        0.98\n", "            banded gecko          50         0.7        0.88\n", "            green iguana          50        0.76        0.94\n", "          Carolina anole          50        0.58        0.96\n", "desert grassland whiptail lizard          50        0.82        0.94\n", "                   agama          50        0.74        0.92\n", "   frilled-necked lizard          50        0.84        0.86\n", "        alligator lizard          50        0.58        0.78\n", "            Gila monster          50        0.72         0.8\n", "   European green lizard          50        0.42         0.9\n", "               chameleon          50        0.76        0.84\n", "           Komodo dragon          50        0.86        0.96\n", "          Nile crocodile          50         0.7        0.84\n", "      American alligator          50        0.76        0.96\n", "             triceratops          50         0.9        0.94\n", "              worm snake          50        0.76        0.88\n", "       ring-necked snake          50         0.8        0.92\n", " eastern hog-nosed snake          50        0.58        0.88\n", "      smooth green snake          50         0.6        0.94\n", "               kingsnake          50        0.82         0.9\n", "            garter snake          50        0.88        0.94\n", "             water snake          50         0.7        0.94\n", "              vine snake          50        0.66        0.76\n", "             night snake          50        0.34        0.82\n", "         boa constrictor          50         0.8        0.96\n", "     African rock python          50        0.48        0.76\n", "            Indian cobra          50        0.82        0.94\n", "             green mamba          50        0.54        0.86\n", "               sea snake          50        0.62         0.9\n", "    Saharan horned viper          50        0.56        0.86\n", "eastern diamondback rattlesnake          50         0.6        0.86\n", "              sidewinder          50        0.28        0.86\n", "               trilobite          50        0.98        0.98\n", "              harvestman          50        0.86        0.94\n", "                scorpion          50        0.86        0.94\n", "    yellow garden spider          50        0.92        0.96\n", "             barn spider          50        0.38        0.98\n", "  European garden spider          50        0.62        0.98\n", "    southern black widow          50        0.88        0.94\n", "               tarantula          50        0.94           1\n", "             wolf spider          50        0.82        0.92\n", "                    tick          50        0.74        0.84\n", "               centipede          50        0.68        0.82\n", "            black grouse          50        0.88        0.98\n", "               ptarmigan          50        0.78        0.94\n", "           ruffed grouse          50        0.88           1\n", "          prairie grouse          50        0.92           1\n", "                 peacock          50        0.88         0.9\n", "                   quail          50         0.9        0.94\n", "               partridge          50        0.74        0.96\n", "             grey parrot          50         0.9        0.96\n", "                   macaw          50        0.88        0.98\n", "sulphur-crested cockatoo          50        0.86        0.92\n", "                lorikeet          50        0.96           1\n", "                  coucal          50        0.82        0.88\n", "               bee eater          50        0.96        0.98\n", "                hornbill          50         0.9        0.96\n", "             hummingbird          50        0.88        0.96\n", "                 jacamar          50        0.92        0.94\n", "                  toucan          50        0.84        0.94\n", "                    duck          50        0.76        0.94\n", "  red-breasted merganser          50        0.86        0.96\n", "                   goose          50        0.74        0.96\n", "              black swan          50        0.94        0.98\n", "                  tusker          50        0.54        0.92\n", "                 echidna          50        0.98           1\n", "                platypus          50        0.72        0.84\n", "                 wallaby          50        0.78        0.88\n", "                   koala          50        0.84        0.92\n", "                  wombat          50        0.78        0.84\n", "               jellyfish          50        0.88        0.96\n", "             sea anemone          50        0.72         0.9\n", "             brain coral          50        0.88        0.96\n", "                flatworm          50         0.8        0.98\n", "                nematode          50        0.86         0.9\n", "                   conch          50        0.74        0.88\n", "                   snail          50        0.78        0.88\n", "                    slug          50        0.74        0.82\n", "                sea slug          50        0.88        0.98\n", "                  chiton          50        0.88        0.98\n", "      chambered nautilus          50        0.88        0.92\n", "          Dungeness crab          50        0.78        0.94\n", "               rock crab          50        0.68        0.86\n", "            fiddler crab          50        0.64        0.86\n", "           red king crab          50        0.76        0.96\n", "        American lobster          50        0.78        0.96\n", "           spiny lobster          50        0.74        0.88\n", "                crayfish          50        0.56        0.86\n", "             hermit crab          50        0.78        0.96\n", "                  isopod          50        0.66        0.78\n", "             white stork          50        0.88        0.96\n", "             black stork          50        0.84        0.98\n", "               spoonbill          50        0.96           1\n", "                flamingo          50        0.94           1\n", "       little blue heron          50        0.92        0.98\n", "             great egret          50         0.9        0.96\n", "                 bittern          50        0.86        0.94\n", "            crane (bird)          50        0.62         0.9\n", "                 <PERSON><PERSON>          50        0.98           1\n", "        common gallinule          50        0.92        0.96\n", "           American coot          50         0.9        0.98\n", "                 bustard          50        0.92        0.96\n", "         ruddy turnstone          50        0.94           1\n", "                  dunlin          50        0.86        0.94\n", "         common redshank          50         0.9        0.96\n", "               dowitcher          50        0.84        0.96\n", "           oystercatcher          50        0.86        0.94\n", "                 pelican          50        0.92        0.96\n", "            king penguin          50        0.88        0.96\n", "               albatross          50         0.9           1\n", "              grey whale          50        0.84        0.92\n", "            killer whale          50        0.92           1\n", "                  dugong          50        0.84        0.96\n", "                sea lion          50        0.82        0.92\n", "               Chihuahua          50        0.66        0.84\n", "           Japanese Chin          50        0.72        0.98\n", "                 Maltese          50        0.76        0.94\n", "               Pekingese          50        0.84        0.94\n", "                <PERSON><PERSON>          50        0.74        0.96\n", "    <PERSON>          50        0.88        0.98\n", "                Papillon          50        0.86        0.94\n", "             toy terrier          50        0.48        0.94\n", "     Rhodesian Ridgeback          50        0.76        0.98\n", "            Afghan Hound          50        0.84           1\n", "            Basset Hound          50         0.8        0.92\n", "                  Beagle          50        0.82        0.96\n", "              Bloodhound          50        0.48        0.72\n", "      Bluetick Coonhound          50        0.86        0.94\n", " Black and Tan Coonhound          50        0.54         0.8\n", "Treeing Walker Coonhound          50        0.66        0.98\n", "        English foxhound          50        0.32        0.84\n", "       Redbone Coonhound          50        0.62        0.94\n", "                  borzoi          50        0.92           1\n", "         Irish Wolfhound          50        0.48        0.88\n", "       Italian Greyhound          50        0.76        0.98\n", "                 Whippet          50        0.74        0.92\n", "            Ibizan Ho<PERSON>          50         0.6        0.86\n", "      Norwegian Elkhound          50        0.88        0.98\n", "              Otterhound          50        0.62         0.9\n", "                  Saluki          50        0.72        0.92\n", "      Scottish Deerhound          50        0.86        0.98\n", "              Weimaraner          50        0.88        0.94\n", "Staffordshire Bull Terrier          50        0.66        0.98\n", "American Staffordshire Terrier          50        0.64        0.92\n", "      Bedlington Terrier          50         0.9        0.92\n", "          Border Terrier          50        0.86        0.92\n", "      Kerry Blue Terrier          50        0.78        0.98\n", "           Irish Terrier          50         0.7        0.96\n", "         Norfolk Terrier          50        0.68         0.9\n", "         Norwich Terrier          50        0.72           1\n", "       Yorkshire Terrier          50        0.66         0.9\n", "        Wire Fox Terrier          50        0.64        0.98\n", "        Lakeland Terrier          50        0.74        0.92\n", "        Sealyham Terrier          50        0.76         0.9\n", "        Airedale Terrier          50        0.82        0.92\n", "           Cairn Terrier          50        0.76         0.9\n", "      Australian Terrier          50        0.48        0.84\n", "  <PERSON><PERSON> Terrier          50        0.82        0.92\n", "          Boston Terrier          50        0.92           1\n", "     Miniature Schnauzer          50        0.68         0.9\n", "         <PERSON>au<PERSON>          50        0.72        0.98\n", "      Standard Schnauzer          50        0.74           1\n", "        Scottish Terrier          50        0.76        0.96\n", "         Tibetan Terrier          50        0.48           1\n", "Australian Silky Terrier          50        0.66        0.96\n", "Soft-coated Wheaten Terrier          50        0.74        0.96\n", "West Highland White Terrier          50        0.88        0.96\n", "              Lhasa Apso          50        0.68        0.96\n", "   Flat-Coated Retriever          50        0.72        0.94\n", "  Curly-coated Retriever          50        0.82        0.94\n", "        Golden Retriever          50        0.86        0.94\n", "      Labrador Retriever          50        0.82        0.94\n", "Chesapeake Bay Retriever          50        0.76        0.96\n", "German <PERSON><PERSON><PERSON> Pointer          50         0.8        0.96\n", "                  Vizsla          50        0.68        0.96\n", "          English Setter          50         0.7           1\n", "            Irish Setter          50         0.8         0.9\n", "           <PERSON>          50        0.84        0.92\n", "                Brittany          50        0.84        0.96\n", "         <PERSON><PERSON> Spaniel          50        0.92        0.96\n", "English Springer Spaniel          50        0.88           1\n", "  Welsh Springer Spaniel          50        0.92           1\n", "         Cocker Spaniels          50         0.7        0.94\n", "          Sussex Spaniel          50        0.72        0.92\n", "     Irish Water Spaniel          50        0.88        0.98\n", "                  Kuvasz          50        0.66         0.9\n", "              Sc<PERSON>perke          50         0.9        0.98\n", "             Groenendael          50         0.8        0.94\n", "                Malinois          50        0.86        0.98\n", "                  Briard          50        0.52         0.8\n", "       Australian Kelpie          50         0.6        0.88\n", "                <PERSON><PERSON><PERSON>          50        0.88        0.94\n", "    Old English Sheepdog          50        0.94        0.98\n", "       Shetland Sheepdog          50        0.74         0.9\n", "                  collie          50         0.6        0.96\n", "           Border Collie          50        0.74        0.96\n", "    <PERSON><PERSON>vier des Flandres          50        0.78        0.94\n", "              Rottweiler          50        0.88        0.96\n", "     German Shepherd Dog          50         0.8        0.98\n", "               <PERSON><PERSON><PERSON>          50        0.68        0.96\n", "      Miniature Pinscher          50        0.76        0.88\n", "Greater Swiss Mountain Dog          50        0.68        0.94\n", "    Bernese Mountain Dog          50        0.96           1\n", "  Appenzeller Sennenhund          50        0.22           1\n", "  Entlebucher Sennenhund          50        0.64        0.98\n", "                   <PERSON><PERSON>          50         0.7        0.92\n", "             Bullmastiff          50        0.78        0.98\n", "         Tibetan Mastiff          50        0.88        0.96\n", "          French Bulldog          50        0.84        0.94\n", "              Great Dane          50        0.54         0.9\n", "             <PERSON><PERSON>          50        0.92           1\n", "                   husky          50        0.46        0.98\n", "        Alaskan Malamute          50        0.76        0.96\n", "          Siberian Husky          50        0.46        0.98\n", "               Dalmatian          50        0.94        0.98\n", "           <PERSON><PERSON><PERSON><PERSON><PERSON>          50        0.78         0.9\n", "                 Basenji          50        0.92        0.94\n", "                     pug          50        0.94        0.98\n", "              <PERSON><PERSON>          50           1           1\n", "            Newfoundland          50        0.78        0.96\n", "   Pyrenean Mountain Dog          50        0.78        0.96\n", "                 <PERSON><PERSON><PERSON>          50        0.96           1\n", "              Pomeranian          50        0.98           1\n", "               <PERSON>          50         0.9        0.96\n", "                Keeshond          50        0.88        0.94\n", "      Griffon Bruxellois          50        0.84        0.98\n", "    Pembroke Welsh Corgi          50        0.82        0.94\n", "    Cardigan Welsh Corgi          50        0.66        0.98\n", "              Toy Poodle          50        0.52        0.88\n", "        Miniature Poodle          50        0.52        0.92\n", "         Standard Poodle          50         0.8           1\n", "    Mexican hairless dog          50        0.88        0.98\n", "               grey wolf          50        0.82        0.92\n", "     Alaskan tundra wolf          50        0.78        0.98\n", "                red wolf          50        0.48         0.9\n", "                  coyote          50        0.64        0.86\n", "                   dingo          50        0.76        0.88\n", "                   dhole          50         0.9        0.98\n", "        African wild dog          50        0.98           1\n", "                   hyena          50        0.88        0.96\n", "                 red fox          50        0.54        0.92\n", "                 kit fox          50        0.72        0.98\n", "              Arctic fox          50        0.94           1\n", "                grey fox          50         0.7        0.94\n", "               tabby cat          50        0.54        0.92\n", "               tiger cat          50        0.22        0.94\n", "             Persian cat          50         0.9        0.98\n", "             Siamese cat          50        0.96           1\n", "            Egyptian Mau          50        0.54         0.8\n", "                  cougar          50         0.9           1\n", "                    lynx          50        0.72        0.88\n", "                 leopard          50        0.78        0.98\n", "            snow leopard          50         0.9        0.98\n", "                  jaguar          50         0.7        0.94\n", "                    lion          50         0.9        0.98\n", "                   tiger          50        0.92        0.98\n", "                 cheetah          50        0.94        0.98\n", "              brown bear          50        0.94        0.98\n", "     American black bear          50         0.8           1\n", "              polar bear          50        0.84        0.96\n", "              sloth bear          50        0.72        0.92\n", "                mongoose          50         0.7        0.92\n", "                 meerkat          50        0.82        0.92\n", "            tiger beetle          50        0.92        0.94\n", "                 ladybug          50        0.86        0.94\n", "           ground beetle          50        0.64        0.94\n", "         longhorn beetle          50        0.62        0.88\n", "             leaf beetle          50        0.64        0.98\n", "             dung beetle          50        0.86        0.98\n", "       rhinoceros beetle          50        0.86        0.94\n", "                  weevil          50         0.9           1\n", "                     fly          50        0.78        0.94\n", "                     bee          50        0.68        0.94\n", "                     ant          50        0.68        0.78\n", "             grasshopper          50         0.5        0.92\n", "                 cricket          50        0.64        0.92\n", "            stick insect          50        0.64        0.92\n", "               cockroach          50        0.72         0.8\n", "                  mantis          50        0.64        0.86\n", "                  cicada          50         0.9        0.96\n", "              leafhopper          50        0.88        0.94\n", "                lacewing          50        0.78        0.92\n", "               dragonfly          50        0.82        0.98\n", "               damselfly          50        0.82           1\n", "             red admiral          50        0.94        0.96\n", "                 ringlet          50        0.86        0.98\n", "       monarch butterfly          50         0.9        0.92\n", "             small white          50         0.9           1\n", "       sulphur butterfly          50        0.92           1\n", "gossamer-winged butterfly          50        0.88           1\n", "                starfish          50        0.88        0.92\n", "              sea urchin          50        0.84        0.94\n", "            sea cucumber          50        0.66        0.84\n", "       cottontail rabbit          50        0.72        0.94\n", "                    hare          50        0.84        0.96\n", "           Angora rabbit          50        0.94        0.98\n", "                 hamster          50        0.96           1\n", "               porcupine          50        0.88        0.98\n", "            fox squirrel          50        0.76        0.94\n", "                  marmot          50        0.92        0.96\n", "                  beaver          50        0.78        0.94\n", "              guinea pig          50        0.78        0.94\n", "           common sorrel          50        0.96        0.98\n", "                   zebra          50        0.94        0.96\n", "                     pig          50         0.5        0.76\n", "               wild boar          50        0.84        0.96\n", "                 warthog          50        0.84        0.96\n", "            hippopotamus          50        0.88        0.96\n", "                      ox          50        0.48        0.94\n", "           water buffalo          50        0.78        0.94\n", "                   bison          50        0.88        0.96\n", "                     ram          50        0.58        0.92\n", "           bighorn sheep          50        0.66           1\n", "             Alpine ibex          50        0.92        0.98\n", "              hartebeest          50        0.94           1\n", "                  impala          50        0.82        0.96\n", "                 gazelle          50         0.7        0.96\n", "               dromedary          50         0.9           1\n", "                   llama          50        0.82        0.94\n", "                  weasel          50        0.44        0.92\n", "                    mink          50        0.78        0.96\n", "        European polecat          50        0.46         0.9\n", "     black-footed ferret          50        0.68        0.96\n", "                   otter          50        0.66        0.88\n", "                   skunk          50        0.96        0.96\n", "                  badger          50        0.86        0.92\n", "               armadillo          50        0.88         0.9\n", "        three-toed sloth          50        0.96           1\n", "               orangutan          50        0.78        0.92\n", "                 gorilla          50        0.82        0.94\n", "              chimpanzee          50        0.84        0.94\n", "                  gibbon          50        0.76        0.86\n", "                 siamang          50        0.68        0.94\n", "                  guenon          50         0.8        0.94\n", "            patas monkey          50        0.62        0.82\n", "                  baboon          50         0.9        0.98\n", "                 macaque          50         0.8        0.86\n", "                  langur          50         0.6        0.82\n", " black-and-white colobus          50        0.86         0.9\n", "        proboscis monkey          50           1           1\n", "                marmoset          50        0.74        0.98\n", "   white-headed capuchin          50        0.72         0.9\n", "           howler monkey          50        0.86        0.94\n", "                    titi          50         0.5         0.9\n", "<PERSON><PERSON>'s spider monkey          50        0.42         0.8\n", "  common squirrel monkey          50        0.76        0.92\n", "       ring-tailed lemur          50        0.72        0.94\n", "                   indri          50         0.9        0.96\n", "          Asian elephant          50        0.58        0.92\n", "   African bush elephant          50         0.7        0.98\n", "               red panda          50        0.94        0.94\n", "             giant panda          50        0.94        0.98\n", "                   snoek          50        0.74         0.9\n", "                     eel          50         0.6        0.84\n", "             coho salmon          50        0.84        0.96\n", "             rock beauty          50        0.88        0.98\n", "               clownfish          50        0.78        0.98\n", "                sturgeon          50        0.68        0.94\n", "                 garfish          50        0.62         0.8\n", "                lionfish          50        0.96        0.96\n", "              pufferfish          50        0.88        0.96\n", "                  abacus          50        0.74        0.88\n", "                   <PERSON>baya          50        0.84        0.92\n", "           academic gown          50        0.42        0.86\n", "               accordion          50         0.8         0.9\n", "         acoustic guitar          50         0.5        0.76\n", "        aircraft carrier          50         0.8        0.96\n", "                airliner          50        0.92           1\n", "                 airship          50        0.76        0.82\n", "                   altar          50        0.64        0.98\n", "               ambulance          50        0.88        0.98\n", "      amphibious vehicle          50        0.64        0.94\n", "            analog clock          50        0.52        0.92\n", "                  apiary          50        0.82        0.96\n", "                   apron          50         0.7        0.84\n", "         waste container          50         0.4         0.8\n", "           assault rifle          50        0.42        0.84\n", "                backpack          50        0.34        0.64\n", "                  bakery          50         0.4        0.68\n", "            balance beam          50         0.8        0.98\n", "                 balloon          50        0.86        0.96\n", "           ballpoint pen          50        0.52        0.96\n", "                Band-Aid          50         0.7         0.9\n", "                   banjo          50        0.84           1\n", "                bal<PERSON>          50        0.68        0.94\n", "                 barbell          50        0.56         0.9\n", "            barber chair          50         0.7        0.92\n", "              barbershop          50        0.54        0.86\n", "                    barn          50        0.96        0.96\n", "               barometer          50        0.84        0.98\n", "                  barrel          50        0.56        0.88\n", "             wheelbarrow          50        0.66        0.88\n", "                baseball          50        0.74        0.98\n", "              basketball          50        0.88        0.98\n", "                bassinet          50        0.66        0.92\n", "                 bassoon          50        0.74        0.98\n", "            swimming cap          50        0.62        0.88\n", "              bath towel          50        0.54        0.78\n", "                 bathtub          50         0.4        0.88\n", "           station wagon          50        0.66        0.84\n", "              lighthouse          50        0.78        0.94\n", "                  beaker          50        0.52        0.68\n", "            military cap          50        0.84        0.96\n", "             beer bottle          50        0.66        0.88\n", "              beer glass          50         0.6        0.84\n", "                bell-cot          50        0.56        0.96\n", "                     bib          50        0.58        0.82\n", "          tandem bicycle          50        0.86        0.96\n", "                  bikini          50        0.56        0.88\n", "             ring binder          50        0.64        0.84\n", "              binoculars          50        0.54        0.78\n", "               birdhouse          50        0.86        0.94\n", "               boathouse          50        0.74        0.92\n", "               bobsleigh          50        0.92        0.96\n", "                bolo tie          50         0.8        0.94\n", "             poke bonnet          50        0.64        0.86\n", "                bookcase          50        0.66        0.92\n", "               bookstore          50        0.62        0.88\n", "              bottle cap          50        0.58         0.7\n", "                     bow          50        0.72        0.86\n", "                 bow tie          50         0.7         0.9\n", "                   brass          50        0.92        0.96\n", "                     bra          50         0.5         0.7\n", "              breakwater          50        0.62        0.86\n", "             breastplate          50         0.4         0.9\n", "                   broom          50         0.6        0.86\n", "                  bucket          50        0.66         0.8\n", "                  buckle          50         0.5        0.68\n", "        bulletproof vest          50         0.5        0.78\n", "        high-speed train          50        0.94        0.96\n", "            butcher shop          50        0.74        0.94\n", "                 taxicab          50        0.64        0.86\n", "                cauldron          50        0.44        0.66\n", "                  candle          50        0.48        0.74\n", "                  cannon          50        0.88        0.94\n", "                   canoe          50        0.94           1\n", "              can opener          50        0.66        0.86\n", "                cardigan          50        0.68         0.8\n", "              car mirror          50        0.94        0.96\n", "                carousel          50        0.94        0.98\n", "                tool kit          50        0.56        0.78\n", "                  carton          50        0.42         0.7\n", "               car wheel          50        0.38        0.74\n", "automated teller machine          50        0.76        0.94\n", "                cassette          50        0.52         0.8\n", "         cassette player          50        0.28         0.9\n", "                  castle          50        0.78        0.88\n", "               catamaran          50        0.78           1\n", "               CD player          50        0.52        0.82\n", "                   cello          50        0.82           1\n", "            mobile phone          50        0.68        0.86\n", "                   chain          50        0.38        0.66\n", "        chain-link fence          50         0.7        0.84\n", "              chain mail          50        0.64         0.9\n", "                chainsaw          50        0.84        0.92\n", "                   chest          50        0.68        0.92\n", "              chiff<PERSON><PERSON>          50        0.26        0.64\n", "                   chime          50        0.62        0.84\n", "           china cabinet          50        0.82        0.96\n", "      Christmas stocking          50        0.92        0.94\n", "                  church          50        0.62         0.9\n", "           movie theater          50        0.58        0.88\n", "                 cleaver          50        0.32        0.62\n", "          cliff dwelling          50        0.88           1\n", "                   cloak          50        0.32        0.64\n", "                   clogs          50        0.58        0.88\n", "         cocktail shaker          50        0.62         0.7\n", "              coffee mug          50        0.44        0.72\n", "             coffeemaker          50        0.64        0.92\n", "                    coil          50        0.66        0.84\n", "        combination lock          50        0.64        0.84\n", "       computer keyboard          50         0.7        0.82\n", "     confectionery store          50        0.54        0.86\n", "          container ship          50        0.82        0.98\n", "             convertible          50        0.78        0.98\n", "               corkscrew          50        0.82        0.92\n", "                  cornet          50        0.46        0.88\n", "             cowboy boot          50        0.64         0.8\n", "              cowboy hat          50        0.64        0.82\n", "                  cradle          50        0.38         0.8\n", "         crane (machine)          50        0.78        0.94\n", "            crash helmet          50        0.92        0.96\n", "                   crate          50        0.52        0.82\n", "              infant bed          50        0.74           1\n", "               Crock Pot          50        0.78         0.9\n", "            croquet ball          50         0.9        0.96\n", "                  crutch          50        0.46         0.7\n", "                 cuirass          50        0.54        0.86\n", "                     dam          50        0.74        0.92\n", "                    desk          50         0.6        0.86\n", "        desktop computer          50        0.54        0.94\n", "   rotary dial telephone          50        0.88        0.94\n", "                  diaper          50        0.68        0.84\n", "           digital clock          50        0.54        0.76\n", "           digital watch          50        0.58        0.86\n", "            dining table          50        0.76         0.9\n", "               dishcloth          50        0.94           1\n", "              dishwasher          50        0.44        0.78\n", "              disc brake          50        0.98           1\n", "                    dock          50        0.54        0.94\n", "                dog sled          50        0.84           1\n", "                    dome          50        0.72        0.92\n", "                 doormat          50        0.56        0.82\n", "            drilling rig          50        0.84        0.96\n", "                    drum          50        0.38        0.68\n", "               drumstick          50        0.56        0.72\n", "                dumbbell          50        0.62         0.9\n", "              Dutch oven          50         0.7        0.84\n", "            electric fan          50        0.82        0.86\n", "         electric guitar          50        0.62        0.84\n", "     electric locomotive          50        0.92        0.98\n", "    entertainment center          50         0.9        0.98\n", "                envelope          50        0.44        0.86\n", "        espresso machine          50        0.72        0.94\n", "             face powder          50         0.7        0.92\n", "             feather boa          50         0.7        0.84\n", "          filing cabinet          50        0.88        0.98\n", "                fireboat          50        0.94        0.98\n", "             fire engine          50        0.84         0.9\n", "       fire screen sheet          50        0.62        0.76\n", "                flagpole          50        0.74        0.88\n", "                   flute          50        0.36        0.72\n", "           folding chair          50        0.62        0.84\n", "         football helmet          50        0.86        0.94\n", "                forklift          50         0.8        0.92\n", "                fountain          50        0.84        0.94\n", "            fountain pen          50        0.76        0.92\n", "         four-poster bed          50        0.78        0.94\n", "             freight car          50        0.96           1\n", "             French horn          50        0.76        0.92\n", "              frying pan          50        0.36        0.78\n", "                fur coat          50        0.84        0.96\n", "           garbage truck          50         0.9        0.98\n", "                gas mask          50        0.84        0.92\n", "                gas pump          50         0.9        0.98\n", "                  goblet          50        0.68        0.82\n", "                 go-kart          50         0.9           1\n", "               golf ball          50        0.84         0.9\n", "               golf cart          50        0.78        0.86\n", "                 gondola          50        0.98        0.98\n", "                    gong          50        0.74        0.92\n", "                    gown          50        0.62        0.96\n", "             grand piano          50         0.7        0.96\n", "              greenhouse          50         0.8        0.98\n", "                  grille          50        0.72         0.9\n", "           grocery store          50        0.66        0.94\n", "              guillotine          50        0.86        0.92\n", "                barrette          50        0.52        0.66\n", "              hair spray          50         0.5        0.74\n", "              half-track          50        0.78         0.9\n", "                  hammer          50        0.56        0.76\n", "                  hamper          50        0.64        0.84\n", "              hair dryer          50        0.56        0.74\n", "      hand-held computer          50        0.42        0.86\n", "            handkerchief          50        0.78        0.94\n", "         hard disk drive          50        0.76        0.84\n", "               harmonica          50         0.7        0.88\n", "                    harp          50        0.88        0.96\n", "               harvester          50        0.78           1\n", "                 hatchet          50        0.54        0.74\n", "                 holster          50        0.66        0.84\n", "            home theater          50        0.64        0.94\n", "               honeycomb          50        0.56        0.88\n", "                    hook          50         0.3         0.6\n", "              hoop skirt          50        0.64        0.86\n", "          horizontal bar          50        0.68        0.98\n", "     horse-drawn vehicle          50        0.88        0.94\n", "               hourglass          50        0.88        0.96\n", "                    iPod          50        0.76        0.94\n", "            clothes iron          50        0.82        0.88\n", "         jack-o'-lantern          50        0.98        0.98\n", "                   jeans          50        0.68        0.84\n", "                    jeep          50        0.72         0.9\n", "                 T-shirt          50        0.72        0.96\n", "           jigsaw puzzle          50        0.84        0.94\n", "         pulled rickshaw          50        0.86        0.94\n", "                joystick          50         0.8         0.9\n", "                  kimono          50        0.84        0.96\n", "                knee pad          50        0.62        0.88\n", "                    knot          50        0.66         0.8\n", "                lab coat          50         0.8        0.96\n", "                   ladle          50        0.36        0.64\n", "               lampshade          50        0.48        0.84\n", "         laptop computer          50        0.26        0.88\n", "              lawn mower          50        0.78        0.96\n", "                lens cap          50        0.46        0.72\n", "             paper knife          50        0.26         0.5\n", "                 library          50        0.54         0.9\n", "                lifeboat          50        0.92        0.98\n", "                 lighter          50        0.56        0.78\n", "               limousine          50        0.76        0.92\n", "             ocean liner          50        0.88        0.94\n", "                lipstick          50        0.74         0.9\n", "            slip-on shoe          50        0.74        0.92\n", "                  lotion          50         0.5        0.86\n", "                 speaker          50        0.52        0.68\n", "                   loupe          50        0.32        0.52\n", "                 sawmill          50        0.72         0.9\n", "        magnetic compass          50        0.52        0.82\n", "                mail bag          50        0.68        0.92\n", "                 mailbox          50        0.82        0.92\n", "                  tights          50        0.22        0.94\n", "               tank suit          50        0.24         0.9\n", "           manhole cover          50        0.96        0.98\n", "                  maraca          50        0.74         0.9\n", "                 marimba          50        0.84        0.94\n", "                    mask          50        0.44        0.82\n", "                   match          50        0.66         0.9\n", "                 maypole          50        0.96           1\n", "                    maze          50         0.8        0.96\n", "           measuring cup          50        0.54        0.76\n", "          medicine chest          50         0.6        0.84\n", "                megalith          50         0.8        0.92\n", "              microphone          50        0.52         0.7\n", "          microwave oven          50        0.48        0.72\n", "        military uniform          50        0.62        0.84\n", "                milk can          50        0.68        0.82\n", "                 minibus          50         0.7           1\n", "               miniskirt          50        0.46        0.76\n", "                 minivan          50        0.38         0.8\n", "                 missile          50         0.4        0.84\n", "                  mitten          50        0.76        0.88\n", "             mixing bowl          50         0.8        0.92\n", "             mobile home          50        0.54        0.78\n", "                 Model T          50        0.92        0.96\n", "                   modem          50        0.58        0.86\n", "               monastery          50        0.44         0.9\n", "                 monitor          50         0.4        0.86\n", "                   moped          50        0.56        0.94\n", "                  mortar          50        0.68        0.94\n", "     square academic cap          50         0.5        0.84\n", "                  mosque          50         0.9           1\n", "            mosquito net          50         0.9        0.98\n", "                 scooter          50         0.9        0.98\n", "           mountain bike          50        0.78        0.96\n", "                    tent          50        0.88        0.96\n", "          computer mouse          50        0.42        0.82\n", "               mousetrap          50        0.76        0.88\n", "              moving van          50         0.4        0.72\n", "                  muzzle          50         0.5        0.72\n", "                    nail          50        0.68        0.74\n", "              neck brace          50        0.56        0.68\n", "                necklace          50        0.86           1\n", "                  nipple          50         0.7        0.88\n", "       notebook computer          50        0.34        0.84\n", "                 obelisk          50         0.8        0.92\n", "                    oboe          50         0.6        0.84\n", "                 ocarina          50         0.8        0.86\n", "                odometer          50        0.96           1\n", "              oil filter          50        0.58        0.82\n", "                   organ          50        0.82         0.9\n", "            oscilloscope          50         0.9        0.96\n", "               overskirt          50         0.2         0.7\n", "            bullock cart          50         0.7        0.94\n", "             oxygen mask          50        0.46        0.84\n", "                  packet          50         0.5        0.78\n", "                  paddle          50        0.56        0.94\n", "            paddle wheel          50        0.86        0.96\n", "                 padlock          50        0.74        0.78\n", "              paintbrush          50        0.62         0.8\n", "                 pajamas          50        0.56        0.92\n", "                  palace          50        0.64        0.96\n", "               pan flute          50        0.84        0.86\n", "             paper towel          50        0.66        0.84\n", "               parachute          50        0.92        0.94\n", "           parallel bars          50        0.62        0.96\n", "              park bench          50        0.74         0.9\n", "           parking meter          50        0.84        0.92\n", "           passenger car          50         0.5        0.82\n", "                   patio          50        0.58        0.84\n", "                payphone          50        0.74        0.92\n", "                pedestal          50        0.52         0.9\n", "             pencil case          50        0.64        0.92\n", "        pencil sharpener          50        0.52        0.78\n", "                 perfume          50         0.7         0.9\n", "              Petri dish          50         0.6         0.8\n", "             photocopier          50        0.88        0.98\n", "                plectrum          50         0.7        0.84\n", "             Pickelhaube          50        0.72        0.86\n", "            picket fence          50        0.84        0.94\n", "            pickup truck          50        0.64        0.92\n", "                    pier          50        0.52        0.82\n", "              piggy bank          50        0.82        0.94\n", "             pill bottle          50        0.76        0.86\n", "                  pillow          50        0.76         0.9\n", "          ping-pong ball          50        0.84        0.88\n", "                pinwheel          50        0.76        0.88\n", "             pirate ship          50        0.76        0.94\n", "                 pitcher          50        0.46        0.84\n", "              hand plane          50        0.84        0.94\n", "             planetarium          50        0.88        0.98\n", "             plastic bag          50        0.36        0.62\n", "              plate rack          50        0.52        0.78\n", "                    plow          50        0.78        0.88\n", "                 plunger          50        0.42         0.7\n", "         Polaroid camera          50        0.84        0.92\n", "                    pole          50        0.38        0.74\n", "              police van          50        0.76        0.94\n", "                  poncho          50        0.58        0.86\n", "          billiard table          50         0.8        0.88\n", "             soda bottle          50        0.56        0.94\n", "                     pot          50        0.78        0.92\n", "          potter's wheel          50         0.9        0.94\n", "             power drill          50        0.42        0.72\n", "              prayer rug          50         0.7        0.86\n", "                 printer          50        0.54        0.86\n", "                  prison          50         0.7         0.9\n", "              projectile          50        0.28         0.9\n", "               projector          50        0.62        0.84\n", "             hockey puck          50        0.92        0.96\n", "            punching bag          50         0.6        0.68\n", "                   purse          50        0.42        0.78\n", "                   quill          50        0.68        0.84\n", "                   quilt          50        0.64         0.9\n", "                race car          50        0.72        0.92\n", "                  racket          50        0.72         0.9\n", "                radiator          50        0.66        0.76\n", "                   radio          50        0.64        0.92\n", "         radio telescope          50         0.9        0.96\n", "             rain barrel          50         0.8        0.98\n", "    recreational vehicle          50        0.84        0.94\n", "                    reel          50        0.72        0.82\n", "           reflex camera          50        0.72        0.92\n", "            refrigerator          50         0.7         0.9\n", "          remote control          50         0.7        0.88\n", "              restaurant          50         0.5        0.66\n", "                revolver          50        0.82           1\n", "                   rifle          50        0.38         0.7\n", "           rocking chair          50        0.62        0.84\n", "              rotisserie          50        0.88        0.92\n", "                  eraser          50        0.54        0.76\n", "              rugby ball          50        0.86        0.94\n", "                   ruler          50        0.68        0.86\n", "            running shoe          50        0.78        0.94\n", "                    safe          50        0.82        0.92\n", "              safety pin          50         0.4        0.62\n", "             salt shaker          50        0.66         0.9\n", "                  sandal          50        0.66        0.86\n", "                  sarong          50        0.64        0.86\n", "               saxophone          50        0.66        0.88\n", "                scabbard          50        0.76        0.92\n", "          weighing scale          50        0.58        0.78\n", "              school bus          50        0.92           1\n", "                schooner          50        0.84           1\n", "              scoreboard          50         0.9        0.96\n", "              CRT screen          50        0.14         0.7\n", "                   screw          50         0.9        0.98\n", "             screwdriver          50         0.3        0.58\n", "               seat belt          50        0.88        0.94\n", "          sewing machine          50        0.76         0.9\n", "                  shield          50        0.56        0.82\n", "              shoe store          50        0.78        0.96\n", "                   shoji          50         0.8        0.92\n", "         shopping basket          50        0.52        0.88\n", "           shopping cart          50        0.76        0.92\n", "                  shovel          50        0.62        0.84\n", "              shower cap          50         0.7        0.84\n", "          shower curtain          50        0.64        0.82\n", "                     ski          50        0.74        0.92\n", "                ski mask          50        0.72        0.88\n", "            sleeping bag          50        0.68         0.8\n", "              slide rule          50        0.72        0.88\n", "            sliding door          50        0.44        0.78\n", "            slot machine          50        0.94        0.98\n", "                 snorkel          50        0.86        0.98\n", "              snowmobile          50        0.88           1\n", "                snowplow          50        0.84        0.98\n", "          soap dispenser          50        0.56        0.86\n", "             soccer ball          50        0.86        0.96\n", "                    sock          50        0.62        0.76\n", " solar thermal collector          50        0.72        0.96\n", "                sombrero          50         0.6        0.84\n", "               soup bowl          50        0.56        0.94\n", "               space bar          50        0.34        0.88\n", "            space heater          50        0.52        0.74\n", "           space shuttle          50        0.82        0.96\n", "                 spatula          50         0.3         0.6\n", "               motorboat          50        0.86           1\n", "              spider web          50         0.7         0.9\n", "                 spindle          50        0.86        0.98\n", "              sports car          50         0.6        0.94\n", "               spotlight          50        0.26         0.6\n", "                   stage          50        0.68        0.86\n", "        steam locomotive          50        0.94           1\n", "     through arch bridge          50        0.84        0.96\n", "              steel drum          50        0.82         0.9\n", "             stethoscope          50         0.6        0.82\n", "                   scarf          50         0.5        0.92\n", "              stone wall          50        0.76         0.9\n", "               stopwatch          50        0.58         0.9\n", "                   stove          50        0.46        0.74\n", "                strainer          50        0.64        0.84\n", "                    tram          50        0.88        0.96\n", "               stretcher          50         0.6         0.8\n", "                   couch          50         0.8        0.96\n", "                   stupa          50        0.88        0.88\n", "               submarine          50        0.72        0.92\n", "                    suit          50         0.4        0.78\n", "                 sundial          50        0.58        0.74\n", "                sunglass          50        0.14        0.58\n", "              sunglasses          50        0.28        0.58\n", "               sunscreen          50        0.32         0.7\n", "       suspension bridge          50         0.6        0.94\n", "                     mop          50        0.74        0.92\n", "              sweatshirt          50        0.28        0.66\n", "                swimsuit          50        0.52        0.82\n", "                   swing          50        0.76        0.84\n", "                  switch          50        0.56        0.76\n", "                 syringe          50        0.62        0.82\n", "              table lamp          50         0.6        0.88\n", "                    tank          50         0.8        0.96\n", "             tape player          50        0.46        0.76\n", "                  teapot          50        0.84           1\n", "              teddy bear          50        0.82        0.94\n", "              television          50         0.6         0.9\n", "             tennis ball          50         0.7        0.94\n", "           thatched roof          50        0.88         0.9\n", "           front curtain          50         0.8        0.92\n", "                 thimble          50         0.6         0.8\n", "       threshing machine          50        0.56        0.88\n", "                  throne          50        0.72        0.82\n", "               tile roof          50        0.72        0.94\n", "                 toaster          50        0.66        0.84\n", "            tobacco shop          50        0.42         0.7\n", "             toilet seat          50        0.62        0.88\n", "                   torch          50        0.64        0.84\n", "              totem pole          50        0.92        0.98\n", "               tow truck          50        0.62        0.88\n", "               toy store          50         0.6        0.94\n", "                 tractor          50        0.76        0.98\n", "      semi-trailer truck          50        0.78        0.92\n", "                    tray          50        0.46        0.64\n", "             trench coat          50        0.54        0.72\n", "                tricycle          50        0.72        0.94\n", "                trimaran          50         0.7        0.98\n", "                  tripod          50        0.58        0.86\n", "          triumphal arch          50        0.92        0.98\n", "              trolleybus          50         0.9           1\n", "                trombone          50        0.54        0.88\n", "                     tub          50        0.24        0.82\n", "               turnstile          50        0.84        0.94\n", "     typewriter keyboard          50        0.68        0.98\n", "                umbrella          50        0.52         0.7\n", "                unicycle          50        0.74        0.96\n", "           upright piano          50        0.76         0.9\n", "          vacuum cleaner          50        0.62         0.9\n", "                    vase          50         0.5        0.78\n", "                   vault          50        0.76        0.92\n", "                  velvet          50         0.2        0.42\n", "         vending machine          50         0.9           1\n", "                vestment          50        0.54        0.82\n", "                 viaduct          50        0.78        0.86\n", "                  violin          50        0.68        0.78\n", "              volleyball          50        0.86           1\n", "             waffle iron          50        0.72        0.88\n", "              wall clock          50        0.54        0.88\n", "                  wallet          50        0.52         0.9\n", "                wardrobe          50        0.68        0.88\n", "       military aircraft          50         0.9        0.98\n", "                    sink          50        0.72        0.96\n", "         washing machine          50        0.78        0.94\n", "            water bottle          50        0.54        0.74\n", "               water jug          50        0.22        0.74\n", "             water tower          50         0.9        0.96\n", "             whiskey jug          50        0.64        0.74\n", "                 whistle          50        0.72        0.84\n", "                     wig          50        0.84         0.9\n", "           window screen          50        0.68         0.8\n", "            window shade          50        0.52        0.76\n", "             Windsor tie          50        0.22        0.66\n", "             wine bottle          50        0.42        0.82\n", "                    wing          50        0.54        0.96\n", "                     wok          50        0.46        0.82\n", "            wooden spoon          50        0.58         0.8\n", "                    wool          50        0.32        0.82\n", "        split-rail fence          50        0.74         0.9\n", "               shipwreck          50        0.84        0.96\n", "                    yawl          50        0.78        0.96\n", "                    yurt          50        0.84           1\n", "                 website          50        0.98           1\n", "              comic book          50        0.62         0.9\n", "               crossword          50        0.84        0.88\n", "            traffic sign          50        0.78         0.9\n", "           traffic light          50         0.8        0.94\n", "             dust jacket          50        0.72        0.94\n", "                    menu          50        0.82        0.96\n", "                   plate          50        0.44        0.88\n", "               guacamole          50         0.8        0.92\n", "                consomme          50        0.54        0.88\n", "                 hot pot          50        0.86        0.98\n", "                  trifle          50        0.92        0.98\n", "               ice cream          50        0.68        0.94\n", "                 ice pop          50        0.62        0.84\n", "                baguette          50        0.62        0.88\n", "                   bagel          50        0.64        0.92\n", "                 pretzel          50        0.72        0.88\n", "            cheeseburger          50         0.9           1\n", "                 hot dog          50        0.74        0.94\n", "           mashed potato          50        0.74         0.9\n", "                 cabbage          50        0.84        0.96\n", "                broccoli          50         0.9        0.96\n", "             cauliflower          50        0.82           1\n", "                <PERSON><PERSON><PERSON>          50        0.74         0.9\n", "        spaghetti squash          50         0.8        0.96\n", "            acorn squash          50        0.82        0.96\n", "        butternut squash          50         0.7        0.94\n", "                cucumber          50         0.6        0.96\n", "               artichoke          50        0.84        0.94\n", "             bell pepper          50        0.84        0.98\n", "                 cardoon          50        0.88        0.94\n", "                mushroom          50        0.38        0.92\n", "            <PERSON>          50         0.9        0.96\n", "              strawberry          50         0.6        0.88\n", "                  orange          50         0.7        0.92\n", "                   lemon          50        0.78        0.98\n", "                     fig          50        0.82        0.96\n", "               pineapple          50        0.86        0.96\n", "                  banana          50        0.84        0.96\n", "               jackfruit          50         0.9        0.98\n", "           custard apple          50        0.86        0.96\n", "             pomegranate          50        0.82        0.98\n", "                     hay          50         0.8        0.92\n", "               carbonara          50        0.88        0.94\n", "         chocolate syrup          50        0.46        0.84\n", "                   dough          50         0.4         0.6\n", "                meatloaf          50        0.58        0.84\n", "                   pizza          50        0.84        0.96\n", "                 pot pie          50        0.68         0.9\n", "                 burrito          50         0.8        0.98\n", "                red wine          50        0.54        0.82\n", "                espresso          50        0.64        0.88\n", "                     cup          50        0.38         0.7\n", "                  eggnog          50        0.38         0.7\n", "                     alp          50        0.54        0.88\n", "                  bubble          50         0.8        0.96\n", "                   cliff          50        0.64           1\n", "              coral reef          50        0.72        0.96\n", "                  geyser          50        0.94           1\n", "               lakeshore          50        0.54        0.88\n", "              promontory          50        0.58        0.94\n", "                   shoal          50         0.6        0.96\n", "                seashore          50        0.44        0.78\n", "                  valley          50        0.72        0.94\n", "                 volcano          50        0.78        0.96\n", "         baseball player          50        0.72        0.94\n", "              bridegroom          50        0.72        0.88\n", "             scuba diver          50         0.8           1\n", "                rapeseed          50        0.94        0.98\n", "                   daisy          50        0.96        0.98\n", "   yellow lady's slipper          50           1           1\n", "                    corn          50         0.4        0.88\n", "                   acorn          50        0.92        0.98\n", "                rose hip          50        0.92        0.98\n", "     horse chestnut seed          50        0.94        0.98\n", "            coral fungus          50        0.96        0.96\n", "                  agaric          50        0.82        0.94\n", "               gyromitra          50        0.98           1\n", "      stinkhorn mushroom          50         0.8        0.94\n", "              earth star          50        0.98           1\n", "        hen-of-the-woods          50         0.8        0.96\n", "                  bolete          50        0.74        0.94\n", "                     ear          50        0.48        0.94\n", "            toilet paper          50        0.36        0.68\n", "Speed: 0.1ms pre-process, 0.3ms inference, 0.0ms post-process per image at shape (1, 3, 224, 224)\n", "Results saved to \u001b[1mruns/val-cls/exp\u001b[0m\n"]}], "source": ["# Validate YOLOv5s on Imagenet val\n", "!python classify/val.py --weights yolov5s-cls.pt --data ../datasets/imagenet --img 224 --half"]}, {"cell_type": "markdown", "metadata": {"id": "ZY2VXXXu74w5"}, "source": ["# 3. <PERSON>\n", "\n", "<p align=\"\"><a href=\"https://roboflow.com/?ref=ultralytics\"><img width=\"1000\" src=\"https://github.com/ultralytics/assets/raw/master/im/integrations-loop.png\"/></a></p>\n", "Close the active learning loop by sampling images from your inference conditions with the `roboflow` pip package\n", "<br><br>\n", "\n", "Train a YOLOv5s Classification model on the [Imagenette](https://image-net.org/) dataset with `--data imagenet`, starting from pretrained `--pretrained yolov5s-cls.pt`.\n", "\n", "- **Pretrained [Models](https://github.com/ultralytics/yolov5/tree/master/models)** are downloaded\n", "automatically from the [latest YOLOv5 release](https://github.com/ultralytics/yolov5/releases)\n", "- **Training Results** are saved to `runs/train-cls/` with incrementing run directories, i.e. `runs/train-cls/exp2`, `runs/train-cls/exp3` etc.\n", "<br><br>\n", "\n", "A **Mosaic Dataloader** is used for training which combines 4 images into 1 mosaic.\n", "\n", "## Train on Custom Data with Rob<PERSON>low 🌟 NEW\n", "\n", "[Robof<PERSON>](https://roboflow.com/?ref=ultralytics) enables you to easily **organize, label, and prepare** a high quality dataset with your own custom data. Roboflow also makes it easy to establish an active learning pipeline, collaborate with your team on dataset improvement, and integrate directly into your model building workflow with the `roboflow` pip package.\n", "\n", "- Custom Training Example: [https://blog.roboflow.com/train-yolov5-classification-custom-data/](https://blog.roboflow.com/train-yolov5-classification-custom-data/?ref=ultralytics)\n", "- Custom Training Notebook: [![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/drive/1KZiKUAjtARHAfZCXbJRv14-pOnIsBLPV?usp=sharing)\n", "<br>\n", "\n", "<p align=\"\"><a href=\"https://roboflow.com/?ref=ultralytics\"><img width=\"480\" src=\"https://user-images.githubusercontent.com/26833433/202802162-92e60571-ab58-4409-948d-b31fddcd3c6f.png\"/></a></p>Label images lightning fast (including with model-assisted labeling)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "i3oKtE4g-aNn"}, "outputs": [], "source": ["#@title Select YOLOv5 🚀 logger {run: 'auto'}\n", "logger = 'TensorBoard' #@param ['TensorBoard', 'Comet', 'ClearML']\n", "\n", "if logger == 'TensorBoard':\n", "  %load_ext tensorboard\n", "  %tensorboard --logdir runs/train\n", "elif logger == 'Comet':\n", "  %pip install -q comet_ml\n", "  import comet_ml; comet_ml.init()\n", "elif logger == 'ClearML':\n", "  import clearml; clearml.browser_login()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "1NcFxRcFdJ_O", "outputId": "638c55b1-dc45-4eee-cabc-4921dc61faf5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mclassify/train: \u001b[0mmodel=yolov5s-cls.pt, data=imagenette160, epochs=3, batch_size=16, imgsz=224, nosave=False, cache=ram, device=, workers=8, project=runs/train-cls, name=exp, exist_ok=False, pretrained=True, optimizer=Adam, lr0=0.001, decay=5e-05, label_smoothing=0.1, cutoff=None, dropout=None, verbose=False, seed=0, local_rank=-1\n", "\u001b[34m\u001b[1mgithub: \u001b[0mup to date with https://github.com/ultralytics/yolov5 ✅\n", "YOLOv5 🚀 v6.2-258-g7fc7ed7 Python-3.7.15 torch-1.12.1+cu113 CUDA:0 (Tesla T4, 15110MiB)\n", "\n", "\u001b[34m\u001b[1mTensorBoard: \u001b[0mStart with 'tensorboard --logdir runs/train-cls', view at http://localhost:6006/\n", "\n", "Dataset not found ⚠️, missing path /content/datasets/imagenette160, attempting download...\n", "Downloading https://github.com/ultralytics/yolov5/releases/download/v1.0/imagenette160.zip to /content/datasets/imagenette160.zip...\n", "100% 103M/103M [00:09<00:00, 11.1MB/s]\n", "Unzipping /content/datasets/imagenette160.zip...\n", "Dataset download success ✅ (13.2s), saved to \u001b[1m/content/datasets/imagenette160\u001b[0m\n", "\n", "\u001b[34m\u001b[1malbumentations: \u001b[0mRandomResizedCrop(p=1.0, height=224, width=224, scale=(0.08, 1.0), ratio=(0.75, 1.3333333333333333), interpolation=1), HorizontalFlip(p=0.5), ColorJitter(p=0.5, brightness=[0.6, 1.4], contrast=[0.6, 1.4], saturation=[0.6, 1.4], hue=[0, 0]), Normalize(p=1.0, mean=(0.485, 0.456, 0.406), std=(0.229, 0.224, 0.225), max_pixel_value=255.0), ToTensorV2(always_apply=True, p=1.0, transpose_mask=False)\n", "Model summary: 149 layers, 4185290 parameters, 4185290 gradients, 10.5 GFLOPs\n", "\u001b[34m\u001b[1moptimizer:\u001b[0m <PERSON>(lr=0.001) with parameter groups 32 weight(decay=0.0), 33 weight(decay=5e-05), 33 bias\n", "Image sizes 224 train, 224 test\n", "Using 1 dataloader workers\n", "Logging results to \u001b[1mruns/train-cls/exp\u001b[0m\n", "Starting yolov5s-cls.pt training on imagenette160 dataset with 10 classes for 3 epochs...\n", "\n", "     Epoch   GPU_mem  train_loss    val_loss    top1_acc    top5_acc\n", "       1/3    0.348G        1.31        1.09       0.794       0.979: 100% 592/592 [01:02<00:00,  9.47it/s]\n", "       2/3    0.415G        1.09       0.852       0.883        0.99: 100% 592/592 [00:59<00:00, 10.00it/s]\n", "       3/3    0.415G       0.954       0.776       0.907       0.994: 100% 592/592 [00:59<00:00,  9.89it/s]\n", "\n", "Training complete (0.051 hours)\n", "Results saved to \u001b[1mruns/train-cls/exp\u001b[0m\n", "Predict:         python classify/predict.py --weights runs/train-cls/exp/weights/best.pt --source im.jpg\n", "Validate:        python classify/val.py --weights runs/train-cls/exp/weights/best.pt --data /content/datasets/imagenette160\n", "Export:          python export.py --weights runs/train-cls/exp/weights/best.pt --include onnx\n", "PyTorch Hub:     model = torch.hub.load('ultralytics/yolov5', 'custom', 'runs/train-cls/exp/weights/best.pt')\n", "Visualize:       https://netron.app\n", "\n"]}], "source": ["# Train YOLOv5s Classification on Imagenette160 for 3 epochs\n", "!python classify/train.py --img 224 --batch 16 --epochs 3 --data imagenette160 --model yolov5s-cls.pt --cache"]}, {"cell_type": "markdown", "metadata": {"id": "15glLzbQx5u0"}, "source": ["# 4. Visualize"]}, {"cell_type": "markdown", "metadata": {"id": "nWOsI5wJR1o3"}, "source": ["## Comet Logging and Visualization 🌟 NEW\n", "[Comet](https://bit.ly/yolov5-readme-comet) is now fully integrated with YOLOv5. Track and visualize model metrics in real time, save your hyperparameters, datasets, and model checkpoints, and visualize your model predictions with [Comet Custom Panels](https://bit.ly/yolov5-colab-comet-panels)! Comet makes sure you never lose track of your work and makes it easy to share results and collaborate across teams of all sizes! \n", "\n", "Getting started is easy:\n", "```shell\n", "pip install comet_ml  # 1. install\n", "export COMET_API_KEY=<Your API Key>  # 2. paste API key\n", "python train.py --img 640 --epochs 3 --data coco128.yaml --weights yolov5s.pt  # 3. train\n", "```\n", "\n", "To learn more about all of the supported Comet features for this integration, check out the [Comet Tutorial](https://github.com/ultralytics/yolov5/tree/master/utils/loggers/comet). If you'd like to learn more about Comet, head over to our [documentation](https://bit.ly/yolov5-colab-comet-docs). Get started by trying out the Comet Colab Notebook:\n", "[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/drive/1RG0WOQyxlDlo5Km8GogJpIEJlg_5lyYO?usp=sharing)\n", "\n", "<img width=\"1920\" alt=\"yolo-ui\" src=\"https://user-images.githubusercontent.com/26833433/202851203-164e94e1-2238-46dd-91f8-de020e9d6b41.png\">"]}, {"cell_type": "markdown", "metadata": {"id": "Lay2WsTjNJzP"}, "source": ["## ClearML Logging and Automation 🌟 NEW\n", "\n", "[ClearML](https://cutt.ly/yolov5-notebook-clearml) is completely integrated into YOLOv5 to track your experimentation, manage dataset versions and even remotely execute training runs. To enable ClearML (check cells above):\n", "\n", "- `pip install clearml`\n", "- run `clearml-init` to connect to a ClearML server (**deploy your own [open-source server](https://github.com/allegroai/clearml-server)**, or use our [free hosted server](https://cutt.ly/yolov5-notebook-clearml))\n", "\n", "You'll get all the great expected features from an experiment manager: live updates, model upload, experiment comparison etc. but ClearML also tracks uncommitted changes and installed packages for example. Thanks to that ClearML Tasks (which is what we call experiments) are also reproducible on different machines! With only 1 extra line, we can schedule a YOLOv5 training task on a queue to be executed by any number of ClearML Agents (workers).\n", "\n", "You can use ClearML Data to version your dataset and then pass it to YOLOv5 simply using its unique ID. This will help you keep track of your data without adding extra hassle. Explore the [ClearML Tutorial](https://github.com/ultralytics/yolov5/tree/master/utils/loggers/clearml) for details!\n", "\n", "<a href=\"https://cutt.ly/yolov5-notebook-clearml\">\n", "<img alt=\"ClearML Experiment Management UI\" src=\"https://github.com/thepycoder/clearml_screenshots/raw/main/scalars.jpg\" width=\"1280\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "-WPvRbS5Swl6"}, "source": ["## Local Logging\n", "\n", "Training results are automatically logged with [Tensorboard](https://www.tensorflow.org/tensorboard) and [CSV](https://github.com/ultralytics/yolov5/pull/4148) loggers to `runs/train`, with a new experiment directory created for each new training as `runs/train/exp2`, `runs/train/exp3`, etc.\n", "\n", "This directory contains train and val statistics, mosaics, labels, predictions and augmentated mosaics, as well as metrics and charts including precision-recall (PR) curves and confusion matrices. \n", "\n", "<img alt=\"Local logging results\" src=\"https://user-images.githubusercontent.com/26833433/183222430-e1abd1b7-782c-4cde-b04d-ad52926bf818.jpg\" width=\"1280\"/>\n"]}, {"cell_type": "markdown", "metadata": {"id": "Zelyeqbyt3GD"}, "source": ["# Environments\n", "\n", "YOLOv5 may be run in any of the following up-to-date verified environments (with all dependencies including [CUDA](https://developer.nvidia.com/cuda)/[CUDNN](https://developer.nvidia.com/cudnn), [Python](https://www.python.org/) and [PyTorch](https://pytorch.org/) preinstalled):\n", "\n", "- **Notebooks** with free GPU: <a href=\"https://bit.ly/yolov5-paperspace-notebook\"><img src=\"https://assets.paperspace.io/img/gradient-badge.svg\" alt=\"Run on Gradient\"></a> <a href=\"https://colab.research.google.com/github/ultralytics/yolov5/blob/master/tutorial.ipynb\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"></a> <a href=\"https://www.kaggle.com/ultralytics/yolov5\"><img src=\"https://kaggle.com/static/images/open-in-kaggle.svg\" alt=\"Open In Kaggle\"></a>\n", "- **Google Cloud** Deep Learning VM. See [GCP Quickstart Guide](https://github.com/ultralytics/yolov5/wiki/GCP-Quickstart)\n", "- **Amazon** Deep Learning AMI. See [AWS Quickstart Guide](https://github.com/ultralytics/yolov5/wiki/AWS-Quickstart)\n", "- **Docker Image**. See [Docker Quickstart Guide](https://github.com/ultralytics/yolov5/wiki/Docker-Quickstart) <a href=\"https://hub.docker.com/r/ultralytics/yolov5\"><img src=\"https://img.shields.io/docker/pulls/ultralytics/yolov5?logo=docker\" alt=\"Docker Pulls\"></a>\n"]}, {"cell_type": "markdown", "metadata": {"id": "6Qu7Iesl0p54"}, "source": ["# Status\n", "\n", "![YOLOv5 CI](https://github.com/ultralytics/yolov5/actions/workflows/ci-testing.yml/badge.svg)\n", "\n", "If this badge is green, all [YOLOv5 GitHub Actions](https://github.com/ultralytics/yolov5/actions) Continuous Integration (CI) tests are currently passing. CI tests verify correct operation of YOLOv5 training ([train.py](https://github.com/ultralytics/yolov5/blob/master/train.py)), testing ([val.py](https://github.com/ultralytics/yolov5/blob/master/val.py)), inference ([detect.py](https://github.com/ultralytics/yolov5/blob/master/detect.py)) and export ([export.py](https://github.com/ultralytics/yolov5/blob/master/export.py)) on macOS, Windows, and Ubuntu every 24 hours and on every commit.\n"]}, {"cell_type": "markdown", "metadata": {"id": "IEijrePND_2I"}, "source": ["# Appendix\n", "\n", "Additional content below."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "GMusP4OAxFu6"}, "outputs": [], "source": ["# YOLOv5 PyTorch HUB Inference (DetectionModels only)\n", "import torch\n", "\n", "model = torch.hub.load('ultralytics/yolov5', 'yolov5s')  # yolov5n - yolov5x6 or custom\n", "im = 'https://ultralytics.com/images/zidane.jpg'  # file, Path, PIL.Image, OpenCV, nparray, list\n", "results = model(im)  # inference\n", "results.print()  # or .show(), .save(), .crop(), .pandas(), etc."]}], "metadata": {"accelerator": "GPU", "colab": {"name": "YOLOv5 Classification Tutorial", "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.12"}}, "nbformat": 4, "nbformat_minor": 0}