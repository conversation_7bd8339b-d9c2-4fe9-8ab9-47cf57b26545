# Define hooks for code formations
# Will be applied on any updated commit files if a user has installed and linked commit hook

default_language_version:
  python: python3.8

# Define bot property if installed via https://github.com/marketplace/pre-commit-ci
ci:
  autofix_prs: true
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit suggestions'
  autoupdate_schedule: monthly
  # submodules: true

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      # - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-case-conflict
      - id: check-yaml
      - id: check-toml
      - id: pretty-format-json
      - id: check-docstring-first

  - repo: https://github.com/asottile/pyupgrade
    rev: v3.2.0
    hooks:
      - id: pyupgrade
        name: Upgrade code
        args: [ --py37-plus ]

  - repo: https://github.com/PyCQA/isort
    rev: 5.10.1
    hooks:
      - id: isort
        name: Sort imports

  - repo: https://github.com/pre-commit/mirrors-yapf
    rev: v0.32.0
    hooks:
      - id: yapf
        name: YAPF formatting

  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.16
    hooks:
      - id: mdformat
        name: MD formatting
        additional_dependencies:
          - mdformat-gfm
          - mdformat-black
        exclude: "README.md|README_cn.md"

  - repo: https://github.com/asottile/yesqa
    rev: v1.4.0
    hooks:
      - id: yesqa

  - repo: https://github.com/PyCQA/flake8
    rev: 5.0.4
    hooks:
      - id: flake8
        name: PEP8
