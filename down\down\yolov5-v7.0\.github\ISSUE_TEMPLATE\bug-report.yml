name: 🐛 Bug Report
# title: " "
description: Problems with YOLOv5
labels: [bug, triage]
body:
  - type: markdown
    attributes:
      value: |
        Thank you for submitting a YOLOv5 🐛 Bug Report!

  - type: checkboxes
    attributes:
      label: Search before asking
      description: >
        Please search the [issues](https://github.com/ultralytics/yolov5/issues) to see if a similar bug report already exists.
      options:
        - label: >
            I have searched the YOLOv5 [issues](https://github.com/ultralytics/yolov5/issues) and found no similar bug report.
          required: true

  - type: dropdown
    attributes:
      label: YOLOv5 Component
      description: |
        Please select the part of YOLOv5 where you found the bug.
      multiple: true
      options:
        - "Training"
        - "Validation"
        - "Detection"
        - "Export"
        - "PyTorch Hub"
        - "Multi-GPU"
        - "Evolution"
        - "Integrations"
        - "Other"
    validations:
      required: false

  - type: textarea
    attributes:
      label: Bug
      description: Provide console output with error messages and/or screenshots of the bug.
      placeholder: |
        💡 ProTip! Include as much information as possible (screenshots, logs, tracebacks etc.) to receive the most helpful response.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Environment
      description: Please specify the software and hardware you used to produce the bug.
      placeholder: |
        - YOLO: YOLOv5 🚀 v6.0-67-g60e42e1 torch 1.9.0+cu111 CUDA:0 (A100-SXM4-40GB, 40536MiB)
        - OS: Ubuntu 20.04
        - Python: 3.9.0
    validations:
      required: false

  - type: textarea
    attributes:
      label: Minimal Reproducible Example
      description: >
        When asking a question, people will be better able to provide help if you provide code that they can easily understand and use to **reproduce** the problem.
        This is referred to by community members as creating a [minimal reproducible example](https://stackoverflow.com/help/minimal-reproducible-example).
      placeholder: |
        ```
        # Code to reproduce your issue here
        ```
    validations:
      required: false

  - type: textarea
    attributes:
      label: Additional
      description: Anything else you would like to share?

  - type: checkboxes
    attributes:
      label: Are you willing to submit a PR?
      description: >
        (Optional) We encourage you to submit a [Pull Request](https://github.com/ultralytics/yolov5/pulls) (PR) to help improve YOLOv5 for everyone, especially if you have a good understanding of how to implement a fix or feature.
        See the YOLOv5 [Contributing Guide](https://github.com/ultralytics/yolov5/blob/master/CONTRIBUTING.md) to get started.
      options:
        - label: Yes I'd like to help by submitting a PR!
