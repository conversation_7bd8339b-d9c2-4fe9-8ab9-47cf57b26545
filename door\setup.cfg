# Project-wide configuration file, can be used for package metadata and other toll configurations
# Example usage: global configuration for PEP8 (via flake8) setting or default pytest arguments
# Local usage: pip install pre-commit, pre-commit run --all-files

[metadata]
license_file = LICENSE
description_file = README.md


[tool:pytest]
norecursedirs =
    .git
    dist
    build
addopts =
    --doctest-modules
    --durations=25
    --color=yes


[flake8]
max-line-length = 120
exclude = .tox,*.egg,build,temp
select = E,W,F
doctests = True
verbose = 2
# https://pep8.readthedocs.io/en/latest/intro.html#error-codes
format = pylint
# see: https://www.flake8rules.com/
ignore =
    E731  # Do not assign a lambda expression, use a def
    F405  # name may be undefined, or defined from star imports: module
    E402  # module level import not at top of file
    F401  # module imported but unused
    W504  # line break after binary operator
    E127  # continuation line over-indented for visual indent
    E231  # missing whitespace after ‘,’, ‘;’, or ‘:’
    E501  # line too long
    F403  # ‘from module import *’ used; unable to detect undefined names


[isort]
# https://pycqa.github.io/isort/docs/configuration/options.html
line_length = 120
# see: https://pycqa.github.io/isort/docs/configuration/multi_line_output_modes.html
multi_line_output = 0


[yapf]
based_on_style = pep8
spaces_before_comment = 2
COLUMN_LIMIT = 120
COALESCE_BRACKETS = True
SPACES_AROUND_POWER_OPERATOR = True
SPACE_BETWEEN_ENDING_COMMA_AND_CLOSING_BRACKET = False
SPLIT_BEFORE_CLOSING_BRACKET = False
SPLIT_BEFORE_FIRST_ARGUMENT = False
# EACH_DICT_ENTRY_ON_SEPARATE_LINE = False
